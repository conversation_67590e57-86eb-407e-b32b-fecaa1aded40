<?php
$string['auth_onlineconfirmdescription'] = 'Requires email address but no email confirmation.';
$string['auth_onlineconfirmrecaptcha'] = 'Adds a visual/audio confirmation form element to the signup page for email self-registering users. This protects your site against spammers and contributes to a worthwhile cause. See http://www.google.com/recaptcha/learnmore for more details. <br /><em>PHP cURL extension is required.</em>';
$string['auth_onlineconfirmrecaptcha_key'] = 'Enable reCAPTCHA element';
$string['auth_onlineconfirmsettings'] = 'Settings';
$string['pluginname'] = 'Online Confirm';
$string['onlineconfirm'] = '<p>Uma nova conta foi criada e enviado um usuário e senha temporário para o usuário. Clique no botão abaixo para voltar para pagina inicial</p>';
$string['lang_select'] = 'Idioma';
$string['state_select'] = 'Estado';
$string['city_select'] = 'Cidade';
$string['option_default'] = 'Escolha uma opção';
$string['gender_default'] = 'Sexo';
$string['cpf'] = 'CPF';
$string['cnpj'] = 'CNPJ empresa atendida';
$string['raca_select'] = 'Cor/Raça';
$string['formacao_select'] = 'Grau de Instrução';
$string['birth_date'] = 'Data de Nascimento';
$string['phone2'] = 'Celular';
$string['codconsultor'] = 'Código do Mentor';
$string['idlean'] = 'ID Lean';
$string['required'] = 'Esse campo é Obrigatório';
$string['personaldata'] = 'Dados do Aluno';
$string['statesresidence'] = 'Informação de Residência';
$string['homestate_select'] = 'Estado Natal';
$string['homecity_select'] = 'Cidade Natal';
$string['profile_salve'] = 'Salvar os dados';
$string['createaccount'] = 'Criar uma nova conta';
$string['profile_heading'] = 'Complete com seus Dados';
$string['profile_title'] = 'Complete com seus Dados';
$string['headercodconsultor'] = 'Dados do Mentor';
$string['headercpnj'] = 'Dados da Empresa';
$string['invalidconsultor'] = 'O codconsultor fornecido não existe ou não está ativo.';
