<?php
$string['auth_onlineconfirmdescription'] = 'Requires email address but no email confirmation.';
$string['auth_onlineconfirmrecaptcha'] = 'Adds a visual/audio confirmation form element to the signup page for email self-registering users. This protects your site against spammers and contributes to a worthwhile cause. See http://www.google.com/recaptcha/learnmore for more details. <br /><em>PHP cURL extension is required.</em>';
$string['auth_onlineconfirmrecaptcha_key'] = 'Enable reCAPTCHA element';
$string['auth_onlineconfirmsettings'] = 'Settings';
$string['pluginname'] = 'Online Confirm';
$string['onlineconfirm'] = '<p>Please click on the link below to confirm your new account.</p>';
$string['lang_select'] = 'Language';
$string['state_select'] = 'State';
$string['city_select'] = 'City';
$string['option_default'] = 'choose an option';
$string['gender_default'] = 'Gender';
$string['cpf'] = 'CPF';
$string['raca_select'] = 'Color/Race';
$string['formacao_select'] = 'Education Degree';
$string['birth_date'] = 'Date of Birth';
$string['phone2'] = 'Cell Phone';
$string['cnpj'] = 'CNPJ empresa';
$string['codconsultor'] = 'Mentor Code';
$string['idlean'] = 'Lean ID';
$string['required'] = 'This field is required';
$string['personaldata'] = 'Personal data';
$string['statesresidence'] = 'Residence Information';
$string['homestate_select'] = 'Home State';
$string['homecity_select'] = 'Hometown';
$string['profile_salve'] = 'Save the data';
$string['profile_heading'] = 'Complete com seus Dados';
$string['profile_title'] = 'Complete com seus Dados';
$string['createaccount'] = 'Criar uma nova conta';
$string['headercodconsultor'] = 'Mentor Data';
$string['headercpnj'] = 'Company data';
$string['invalidconsultor'] = 'The provided codconsultant does not exist or is not active.';