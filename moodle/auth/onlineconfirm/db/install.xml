<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="auth/onlineconfirm/db" VERSION="2015081003" COMMENT="XMLDB file for Moodle auth/onlineconfirm"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
        <TABLE NAME="auth_onlineconfirm_estados" COMMENT="List of Brazil States">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="nome" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="codetd" TYPE="char" LENGTH="2" NOTNULL="true" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
    </TABLE>

    <TABLE NAME="auth_onlineconfirm_m" COMMENT="List of Brazil Municipalities">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="codmunicipio" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="codetdmunicipio" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="nomemunicipio" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
    </TABLE>

        <TABLE NAME="auth_onlineconfirm_raca" COMMENT="Cor/Raça">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="codinterno" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="cor" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
    </TABLE>

    <TABLE NAME="auth_onlineconfirm_instrucao" COMMENT="Cor/Raça">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="codinterno" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="grau" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
    </TABLE>

    <TABLE NAME="auth_onlineconfirm_consultor" COMMENT="Códigos do Consultor">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="codconsultor" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
    </TABLE>

    <TABLE NAME="enrolmentor" COMMENT="Tabela para armazenar dados de matrícula com mentor">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="idlean" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="codconsultor" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="idcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="iduser" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="status" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
            <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            <KEY NAME="iduser" TYPE="foreign" FIELDS="iduser" REFTABLE="user" REFFIELDS="id"/>
            <KEY NAME="idcurso" TYPE="foreign" FIELDS="idcurso" REFTABLE="course" REFFIELDS="id"/>
        </KEYS>
        <INDEXES>
            <INDEX NAME="idlean" UNIQUE="false" FIELDS="idlean"/>
            <INDEX NAME="codconsultor" UNIQUE="false" FIELDS="codconsultor"/>
        </INDEXES>
    </TABLE>

  </TABLES>
</XMLDB>
