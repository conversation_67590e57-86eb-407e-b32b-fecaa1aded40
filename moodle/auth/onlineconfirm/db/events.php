<?php

defined('MOODLE_INTERNAL') || die();

$observers = [
    // ['eventname' => '\mod_assign\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_book\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_chat\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_choice\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_data\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_feedback\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_folder\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_forum\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_glossary\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_hvp\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_imscp\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_lesson\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_lti\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_page\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    //['eventname' => '\mod_quiz\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_resource\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_scorm\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_sgenota\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_simplecertificate\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_survey\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_url\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_wiki\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],
    // ['eventname' => '\mod_workshop\event\course_module_viewed', 'callback' => 'auth_onlineconfirm\observer::course_module_viewed'],

    ['eventname' => '\core\event\course_viewed', 'callback' => 'auth_onlineconfirm\observer::course_viewed'],
    ['eventname' => '\core\event\user_enrolment_updated', 'callback' => 'auth_onlineconfirm\observer::user_enrolment_updated'],
];