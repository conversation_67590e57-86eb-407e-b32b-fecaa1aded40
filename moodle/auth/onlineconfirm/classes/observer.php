<?php
namespace auth_onlineconfirm;

defined('MOODLE_INTERNAL') || die();

use moodle_url;

class observer {

    public static function course_viewed(\core\event\base $event) {
        GLOBAL $CFG, $PAGE;

        require_once($CFG->dirroot . '/auth/onlineconfirm/validate_profile.php');

        $courseid = $event->courseid;

        if ($courseid != 1) {
            validate_profile($PAGE->url->out_as_local_url(false));
        }
    }

    /**
     * Observer para mudanças no status de matrícula do usuário
     * Atualiza o status na tabela enrolmentor quando o status da matrícula muda
     */
    public static function user_enrolment_updated(\core\event\user_enrolment_updated $event) {
        global $DB;

        $userid = $event->relateduserid;
        $courseid = $event->courseid;

        // Buscar o registro de matrícula atualizado
        $userenrolment = $DB->get_record('user_enrolments', ['id' => $event->objectid]);

        if ($userenrolment) {
            // Buscar o registro na tabela enrolmentor
            $enrolmentor = $DB->get_record('enrolmentor', [
                'iduser' => $userid,
                'idcurso' => $courseid
            ]);

            if ($enrolmentor) {
                // Atualizar o status baseado no status da matrícula
                // 0 = ENROL_USER_ACTIVE (ativo), 1 = ENROL_USER_SUSPENDED (suspenso)
                $new_status = ($userenrolment->status == 0) ? 1 : 0;

                $enrolmentor->status = $new_status;
                $enrolmentor->timemodified = time();

                $DB->update_record('enrolmentor', $enrolmentor);
            }
        }
    }
}
