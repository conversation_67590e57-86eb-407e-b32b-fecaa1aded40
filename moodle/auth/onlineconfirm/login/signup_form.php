<?php
defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/formslib.php');
require_once($CFG->dirroot.'/user/profile/lib.php');
require_once($CFG->dirroot . '/user/editlib.php');

class login_signup_form extends moodleform implements renderable, templatable {

    function definition() {
        global $USER, $CFG, $PAGE, $DB;
        $PAGE->requires->js_call_amd('auth_onlineconfirm/city_loader', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/homecity_loader', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/lang_select', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/valida_cpf', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/valida_cnpj', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/valida_codconsultor', 'init');
        $PAGE->requires->js_call_amd('auth_onlineconfirm/valida_cel', 'init');
        $idcourse = optional_param('idcourse', 0, PARAM_INT);

        $mform = $this->_form;

        $mform->addElement('header', 'personaldata', get_string('personaldata', 'auth_onlineconfirm'), '');

        // Email fields
        $mform->addElement('text', 'email', get_string('email'), 'maxlength="100" size="25" id="email_field"');
        $mform->setType('email', core_user::get_property_type('email'));
        $mform->addRule('email', get_string('missingemail'), 'required', null, 'client');
        $mform->setForceLtr('email');

        $mform->addElement('text', 'email2', get_string('emailagain'), 'maxlength="100" size="25"');
        $mform->setType('email2', core_user::get_property_type('email'));
        $mform->addRule('email2', get_string('missingemail'), 'required', null, 'client');
        $mform->setForceLtr('email2');

        // CPF field
        $mform->addElement('text', 'cpf', get_string('cpf', 'auth_onlineconfirm'));
        $mform->addRule('cpf', get_string('required'), 'required', null, 'client');
        $mform->setType('cpf', PARAM_TEXT);

        // Username field
        $mform->addElement('text', 'username', get_string('username'), 'maxlength="100" size="25" autocapitalize="none" id="username_field"');
        $mform->setType('username', PARAM_RAW);
        $mform->addRule('username', get_string('missingusername'), 'required', null, 'client');

        if (!empty($CFG->passwordpolicy)) {
            $mform->addElement('static', 'passwordpolicyinfo', '', print_password_policy());
        }

        // Name fields
        $namefields = useredit_get_required_name_fields();
        foreach ($namefields as $field) {
            $mform->addElement('text', $field, get_string($field), 'maxlength="100" size="30"');
            $mform->setType($field, core_user::get_property_type('firstname'));
            $stringid = 'missing' . $field;
            if (!get_string_manager()->string_exists($stringid, 'moodle')) {
                $stringid = 'required';
            }
            $mform->addRule($field, get_string($stringid), 'required', null, 'client');
        }

        // Phone field
        $mform->addElement('text', 'phone2', get_string('phone2', 'auth_onlineconfirm'));
        $mform->addRule('phone2', get_string('required', 'auth_onlineconfirm'), 'required', null, 'client');
        $mform->setType('phone2', PARAM_TEXT);

        // Consultant Code field
        $mform->addElement('header', 'headercodconsultor', get_string('headercodconsultor', 'auth_onlineconfirm'), '');
        $mform->addElement('text', 'codconsultor', get_string('codconsultor', 'auth_onlineconfirm'));
        $mform->addRule('codconsultor', get_string('required', 'auth_onlineconfirm'), 'required', null, 'client');
        $mform->setType('codconsultor', PARAM_TEXT);

        // ID Lean field
        $mform->addElement('text', 'idlean', get_string('idlean', 'auth_onlineconfirm'));
        $mform->addRule('idlean', get_string('required', 'auth_onlineconfirm'), 'required', null, 'client');
        $mform->setType('idlean', PARAM_TEXT);

        // CNPJ field
        $mform->addElement('text', 'cnpj', get_string('cnpj', 'auth_onlineconfirm'));
        $mform->addRule('cnpj', get_string('required', 'auth_onlineconfirm'), 'required', null, 'client');
        $mform->setType('cnpj', PARAM_TEXT);

        // Hidden course ID
        $mform->addElement('hidden', 'idcourse');
        $mform->setType('idcourse', PARAM_INT);
        $mform->setDefault('idcourse', $idcourse);

        // Disable submit button if fields are empty
        $mform->disabledIf('submitbutton', 'codconsultor', 'eq', '');
        $mform->disabledIf('submitbutton', 'cpf', 'eq', '');
        $mform->disabledIf('submitbutton', 'idlean', 'eq', '');

        // Additional fields
        profile_signup_fields($mform);

        // CAPTCHA
        if (signup_captcha_enabled()) {
            $mform->addElement('recaptcha', 'recaptcha_element', get_string('security_question', 'auth'));
            $mform->addHelpButton('recaptcha_element', 'recaptcha', 'auth');
            $mform->closeHeaderBefore('recaptcha_element');
        }

        // Submit button
        $this->add_action_buttons(true, get_string('createaccount', 'auth_onlineconfirm'));
    }

    function definition_after_data(){
        $mform = $this->_form;
        $mform->applyFilter('username', 'trim');

        foreach (useredit_get_required_name_fields() as $field) {
            $mform->applyFilter($field, 'trim');
        }
    }

    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        if (signup_captcha_enabled()) {
            $recaptchaelement = $this->_form->getElement('recaptcha_element');
            if (!empty($this->_form->_submitValues['g-recaptcha-response'])) {
                $response = $this->_form->_submitValues['g-recaptcha-response'];
                if (!$recaptchaelement->verify($response)) {
                    $errors['recaptcha_element'] = get_string('incorrectpleasetryagain', 'auth');
                }
            } else {
                $errors['recaptcha_element'] = get_string('missingrecaptchachallengefield');
            }
        }

        $errors += signup_validate_data($data, $files);

        return $errors;
    }

    public function export_for_template(renderer_base $output) {
        ob_start();
        $this->display();
        $formhtml = ob_get_contents();
        ob_end_clean();
        $context = [
            'formhtml' => $formhtml
        ];
        return $context;
    }
}

echo '
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function() {
        const emailField = document.getElementById("email_field");
        const usernameField = document.getElementById("username_field");
        const email2Field = document.getElementById("id_email2");
        const cpf = document.getElementById("id_cpf");

        // Bloqueia o campo username para edição
        usernameField.setAttribute("readonly", true);

        // Atualiza o campo username baseado no CPF
        cpf.addEventListener("input", function() {
            const cpfSemPontosETracos = cpf.value.replace(/[.-]/g, "");
            usernameField.value = cpfSemPontosETracos;
            email2Field.value = emailField.value;
        });
    });
</script>
';