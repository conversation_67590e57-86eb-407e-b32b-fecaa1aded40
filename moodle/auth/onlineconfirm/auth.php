<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Authentication Plugin: Online Confirm Authentication
 *
 * <AUTHOR> Busch
 * @license http://www.gnu.org/copyleft/gpl.html GNU Public License
 * @package auth_onlineconfirm
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/authlib.php');

/**
 * Online Confirm authentication plugin.
 */
class auth_plugin_onlineconfirm extends auth_plugin_base
{

    /**
     * Constructor.
     */
    function __construct()
    {
        $this->authtype = 'onlineconfirm';
        $this->config = get_config('auth/onlineconfirm');
    }

    /**
     * Returns true if the username and password work and false if they are
     * wrong or don't exist.
     *
     * @param string $username The username
     * @param string $password The password
     * @return bool Authentication success or failure.
     */
    function user_login($username, $password)
    {
        global $CFG, $DB;
        if ($user = $DB->get_record('user', array('username' => $username, 'mnethostid' => $CFG->mnet_localhost_id))) {
            return validate_internal_user_password($user, $password);
        }
        return false;
    }

    /**
     * Updates the user's password.
     *
     * called when the user password is updated.
     *
     * @param  object $user User table object  (with system magic quotes)
     * @param  string $newpassword Plaintext password (with system magic quotes)
     * @return boolean result
     *
     */
    function user_update_password($user, $newpassword)
    {
        $user = get_complete_user_data('id', $user->id);
        // This will also update the stored hash to the latest algorithm
        // if the existing hash is using an out-of-date algorithm (or the
        // legacy md5 algorithm).
        return update_internal_user_password($user, $newpassword);
    }

    function can_signup()
    {
        return true;
    }

//Override signup form charbush
    function signup_form()
    {
        global $CFG;

        require_once($CFG->dirroot . '/auth/' . $this->authtype . '/login/signup_form.php');
        return new login_signup_form();
    }


    /**
     * Sign up a new user ready for confirmation.
     * Password is passed in plaintext.
     *
     * @param object $user new user object
     * @param boolean $notify print notice with link and terminate
     */
    function user_signup($user, $notify = true)
    {
        global $CFG, $DB;
        require_once($CFG->dirroot . '/user/profile/lib.php');
        require_once($CFG->dirroot . '/user/lib.php');
        require_once($CFG->dirroot. '/lib/moodlelib.php');

        $consultor = $DB->get_record('auth_onlineconfirm_consultor', ['codconsultor' => $user->codconsultor, 'status' => 1]);

        if (!$consultor) {
            $returnurl = new moodle_url('auth/onlineconfirm/login/signup.php', ['id' =>$user->idcourse]);
            throw new moodle_exception('invalidconsultor', 'auth_onlineconfirm', $returnurl, null, 'O codconsultor fornecido não existe ou não está ativo.');
        }

        $user->password = 'senai2024';

        $user->password = hash_internal_user_password($user->password);
        if (empty($user->calendartype)) {
            $user->calendartype = $CFG->calendartype;
        }

        $user->confirmed = 1;

        $user->id = user_create_user($user, false, false);
		$user->idnumber = $user->id;
		$user->department = 'aluno';

        user_update_user($user, false, false);

        $user->profile_field_cpf = $user->cpf;
        $user->profile_field_cnpj = $user->cnpj;
        $user->profile_field_codconsultor = $user->codconsultor;

        // Save any custom profile field information.
        profile_save_data($user);

        $enrol = enrol_get_plugin('manual');
        $instances = enrol_get_instances($user->idcourse, true);
        $manualinstance = null;

        foreach ($instances as $instance) {
            if ($instance->enrol === 'manual') {
                $manualinstance = $instance;
                break;
            }
        }

        $enrol->enrol_user($manualinstance, $user->id, 5);

        // Inserir dados na tabela auth_onlineconfirm_enrolmentor
        $enrolmentor_record = new stdClass();
        $enrolmentor_record->idlean = $user->idlean;
        $enrolmentor_record->codconsultor = $user->codconsultor;
        $enrolmentor_record->idcurso = $user->idcourse;
        $enrolmentor_record->iduser = $user->id;
        $enrolmentor_record->status = 1; // 1 = ativo
        $enrolmentor_record->timecreated = time();
        $enrolmentor_record->timemodified = time();

        $DB->insert_record('auth_onlineconfirm_enrolmentor', $enrolmentor_record);

        $urlhome = new moodle_url('/');
        $from = core_user::get_noreply_user();
        $to = $user;
        $subject = get_string('email_subject', 'auth_onlineconfirm');
        $messagehtml = get_string('email_welcome_html', 'auth_onlineconfirm', [
            'firstname' => $user->firstname,
            'username' => $user->username,
            'password' => 'senai2024',
            'loginurl' => $urlhome
        ]);

        $message = html_to_text($messagehtml);

        email_to_user($to, $from, $subject, $message, $messagehtml);

        // Trigger event.
        \core\event\user_created::create_from_userid($user->id)->trigger();

        $newuser = get_complete_user_data('id', $user->id);
        //Força troca de senha
        //set_user_preference('auth_forcepasswordchange', 1, $newuser->id);

        if ($notify) {
            global $CFG, $PAGE, $OUTPUT;
            $emailconfirm = get_string('emailconfirm');
            $PAGE->navbar->add($emailconfirm);
            $PAGE->set_title($emailconfirm);
            $PAGE->set_heading($PAGE->course->fullname);
            echo $OUTPUT->header();
           // $this->notice_onlineconfirm(get_string('onlineconfirm', 'auth_onlineconfirm'), $CFG->wwwroot . '/login/confirm.php?data=' . $user->secret . '/' . urlencode($user->username));
           $this->notice_onlineconfirm(get_string('onlineconfirm', 'auth_onlineconfirm'), $CFG->wwwroot . '/mentor');
        } else {
            return true;
        }
    }

    public function notice_onlineconfirm($message, $link = '', $course = null)
    {
        global $PAGE, $OUTPUT;

        $message = clean_text($message);   // In case nasties are in here.

        if (CLI_SCRIPT) {
            echo("!!$message!!\n");
            exit(1); // No success.
        }

        if (!$PAGE->headerprinted) {
            // Header not yet printed.
            $PAGE->set_title(get_string('notice'));
            echo $OUTPUT->header();
        } else {
            echo $OUTPUT->container_end_all(false);
        }

        echo "<div>\n";
        echo $OUTPUT->box($message, 'generalbox', 'notice');
        echo "<div>\n";
        echo $this->continue_button_onlinconfirm($link);

        echo $OUTPUT->footer();
        exit(1); // General error code.
    }


    public function continue_button_onlinconfirm($url)
    {
        if (!($url instanceof moodle_url)) {
            $url = new moodle_url($url);
        }

        echo \html_writer::start_div('', array('class' => 'row justify-content-center'));
        $button = \html_writer::tag('button', '<i class="icon fa fa-check fa-fw"></i>', ['class' => 'btn btn-primary']);
        $addurl = $url;
        return \html_writer::link($addurl, $button);
    }

    /**
     * Returns true if plugin allows confirming of new users.
     *
     * @return bool
     */
    function can_confirm()
    {
        return true;
    }

    /**
     * Confirm the new user as registered.
     *
     * @param string $username
     * @param string $confirmsecret
     */
    function user_confirm($username, $confirmsecret)
    {
        global $DB;
        $user = get_complete_user_data('username', $username);

        if (!empty($user)) {
            if ($user->confirmed) {
                return AUTH_CONFIRM_ALREADY;

            } else if ($user->auth != $this->authtype) {
                return AUTH_CONFIRM_ERROR;

            } else if ($user->secret == $confirmsecret) {   // They have provided the secret key to get in
                $DB->set_field("user", "confirmed", 1, array("id" => $user->id));
                if ($user->firstaccess == 0) {
                    $DB->set_field("user", "firstaccess", time(), array("id" => $user->id));
                }
                return AUTH_CONFIRM_OK;
            }
        } else {
            return AUTH_CONFIRM_ERROR;
        }
    }

    function prevent_local_passwords()
    {
        return false;
    }

    /**
     * Returns true if this authentication plugin is 'internal'.
     *
     * @return bool
     */
    function is_internal()
    {
        return true;
    }

    /**
     * Returns true if this authentication plugin can change the user's
     * password.
     *
     * @return bool
     */
    function can_change_password()
    {
        return true;
    }

    /**
     * Returns the URL for changing the user's pw, or empty if the default can
     * be used.
     *
     * @return moodle_url
     */
    function change_password_url()
    {
        global $CFG;
        //    return new moodle_url($CFG->wwwroot.'/auth/onlineconfirm/reset_password.php');
        return null; // use default internal method
    }

    /**
     * Returns true if plugin allows resetting of internal password.
     *
     * @return bool
     */
    function can_reset_password()
    {
        return true;
    }

    /**
     * Returns true if plugin can be manually set.
     *
     * @return bool
     */
    function can_be_manually_set()
    {
        return true;
    }

    /**
     * Processes and stores configuration data for this authentication plugin.
     */
    function process_config($config)
    {
        // set to defaults if undefined
        if (!isset($config->recaptcha)) {
            $config->recaptcha = false;
        }

        // save settings
        set_config('recaptcha', $config->recaptcha, 'auth/onlineconfirm');
        return true;
    }

    /**
     * Returns whether or not the captcha element is enabled, and the admin settings fulfil its requirements.
     * @return bool
     */
    function is_captcha_enabled()
    {
        global $CFG;
        return isset($CFG->recaptchapublickey) && isset($CFG->recaptchaprivatekey) && get_config("auth/{$this->authtype}", 'recaptcha');
    }

}


